package cn.ykload.flowmix

import cn.ykload.flowmix.data.ApiResponse
import cn.ykload.flowmix.data.DataSource
import cn.ykload.flowmix.data.Headphone
import cn.ykload.flowmix.data.HeadphoneFrequencyData
import cn.ykload.flowmix.data.MeasurementCondition
import org.junit.Assert.assertEquals
import org.junit.Assert.assertFalse
import org.junit.Assert.assertNotNull
import org.junit.Assert.assertTrue
import org.junit.Test

/**
 * 频响数据模型测试
 */
class FrequencyResponseDataTest {

    @Test
    fun testDataSourceCreation() {
        val dataSource = DataSource(
            name = "realab",
            displayName = "Realab",
            description = "Realab耳机频响数据源"
        )
        
        assertEquals("realab", dataSource.name)
        assertEquals("Realab", dataSource.displayName)
        assertEquals("Realab耳机频响数据源", dataSource.description)
    }

    @Test
    fun testHeadphoneWithSourceName() {
        val headphone = Headphone(
            fileName = "Apple_苹果_AirPods_Max",
            originalName = "Apple/苹果 AirPods Max",
            lastUpdated = "2024-01-15T10:30:00.000Z",
            sourceName = "realab"
        )
        
        assertEquals("Apple_苹果_AirPods_Max", headphone.fileName)
        assertEquals("Apple/苹果 AirPods Max", headphone.originalName)
        assertEquals("realab", headphone.sourceName)
    }

    @Test
    fun testHeadphoneFrequencyDataWithSource() {
        val measurementCondition = MeasurementCondition(
            title = "Apple/苹果 AirPods Max - ANC on（B&K 5128）",
            frequencies = listOf(20f, 21f, 22f),
            spl_values = listOf(97.852f, 97.843f, 97.833f)
        )
        
        val frequencyData = HeadphoneFrequencyData(
            sourceName = "realab",
            brandName = "Apple/苹果",
            headphoneName = "Apple/苹果 AirPods Max",
            lastUpdated = "2024-01-15T10:30:00.000Z",
            frequencyData = mapOf("ANC on（B&K 5128）" to measurementCondition)
        )
        
        assertEquals("realab", frequencyData.sourceName)
        assertEquals("Apple/苹果", frequencyData.brandName)
        assertEquals("Apple/苹果 AirPods Max", frequencyData.headphoneName)
        assertTrue(frequencyData.frequencyData.containsKey("ANC on（B&K 5128）"))
    }

    @Test
    fun testMeasurementConditionValidation() {
        // 有效的测量条件
        val validCondition = MeasurementCondition(
            title = "Test Condition",
            frequencies = listOf(20f, 100f, 1000f),
            spl_values = listOf(90f, 95f, 100f)
        )
        assertTrue(validCondition.isValid())
        
        // 无效的测量条件 - 数组长度不匹配
        val invalidCondition = MeasurementCondition(
            title = "Invalid Condition",
            frequencies = listOf(20f, 100f),
            spl_values = listOf(90f, 95f, 100f)
        )
        assertFalse(invalidCondition.isValid())
        
        // 空数据
        val emptyCondition = MeasurementCondition(
            title = "Empty Condition",
            frequencies = emptyList(),
            spl_values = emptyList()
        )
        assertFalse(emptyCondition.isValid())
    }

    @Test
    fun testApiResponseWithSourceName() {
        val dataSources = listOf(
            DataSource("realab", "Realab", "Realab耳机频响数据源"),
            DataSource("huihifi", "HuiHiFi", "HuiHiFi耳机频响数据源")
        )
        
        val response = ApiResponse(
            success = true,
            data = dataSources,
            count = 2,
            sourceName = null,
            brandName = null,
            message = "获取数据源列表成功"
        )
        
        assertTrue(response.success)
        assertEquals(2, response.count)
        assertEquals("获取数据源列表成功", response.message)
        assertNotNull(response.data)
        assertEquals(2, response.data?.size)
    }

    @Test
    fun testBrandsResponseWithSourceName() {
        val brands = listOf("Apple_苹果", "Sony_索尼", "Sennheiser")
        
        val response = ApiResponse(
            success = true,
            data = brands,
            count = 3,
            sourceName = "realab",
            brandName = null,
            message = "获取数据源realab的品牌列表成功"
        )
        
        assertTrue(response.success)
        assertEquals(3, response.count)
        assertEquals("realab", response.sourceName)
        assertEquals("获取数据源realab的品牌列表成功", response.message)
    }
}
