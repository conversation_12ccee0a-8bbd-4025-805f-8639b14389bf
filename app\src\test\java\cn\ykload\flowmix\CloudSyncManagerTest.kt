package cn.ykload.flowmix

import android.content.Context
import cn.ykload.flowmix.auth.AuthManager
import cn.ykload.flowmix.data.*
import cn.ykload.flowmix.storage.DeviceConfigManager
import cn.ykload.flowmix.sync.CloudSyncManager
import cn.ykload.flowmix.sync.SyncCompletionCallback
import io.mockk.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestCoroutineDispatcher
import kotlinx.coroutines.test.runBlockingTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test

/**
 * CloudSyncManager 单元测试
 * 主要测试配置更新的循环同步问题修复
 */
@ExperimentalCoroutinesApi
class CloudSyncManagerTest {

    private lateinit var context: Context
    private lateinit var authManager: AuthManager
    private lateinit var deviceConfigManager: DeviceConfigManager
    private lateinit var scope: CoroutineScope
    private lateinit var syncCallback: SyncCompletionCallback
    private lateinit var cloudSyncManager: CloudSyncManager
    
    private val testDispatcher = TestCoroutineDispatcher()

    @Before
    fun setup() {
        context = mockk(relaxed = true)
        authManager = mockk(relaxed = true)
        deviceConfigManager = mockk(relaxed = true)
        scope = CoroutineScope(testDispatcher)
        syncCallback = mockk(relaxed = true)
        
        // Mock Android Settings.Secure.getString
        mockkStatic(android.provider.Settings.Secure::class)
        every { 
            android.provider.Settings.Secure.getString(any(), any()) 
        } returns "test_device_123"
        
        cloudSyncManager = CloudSyncManager(
            context = context,
            authManager = authManager,
            deviceConfigManager = deviceConfigManager,
            scope = scope,
            syncCompletionCallback = syncCallback
        )
    }

    @Test
    fun `onConfigUpdated should ignore updates from same device`() = runBlockingTest {
        // 准备测试数据
        val deviceConfig = DeviceConfig(
            deviceId = "BLUETOOTH_A2DP_-**********",
            deviceName = "OPPO Enco Free4",
            deviceType = "BLUETOOTH_A2DP",
            lastUpdated = System.currentTimeMillis()
        )
        
        val configUpdateData = ConfigUpdateData(
            deviceId = "BLUETOOTH_A2DP_-**********",
            config = deviceConfig,
            version = 1,
            updatedBy = "test_device_123" // 同一个设备ID
        )
        
        val configUpdatedMessage = ConfigUpdatedMessage(
            data = configUpdateData
        )
        
        // 执行测试
        cloudSyncManager.onConfigUpdated(configUpdatedMessage)
        
        // 验证：不应该调用 updateSingleDeviceConfig
        verify(exactly = 0) { 
            deviceConfigManager.updateSingleDeviceConfig(any()) 
        }
        
        // 验证：不应该调用同步完成回调
        verify(exactly = 0) { 
            syncCallback.onSyncCompleted(any(), any()) 
        }
    }

    @Test
    fun `onConfigUpdated should process updates from different device`() = runBlockingTest {
        // 准备测试数据
        val deviceConfig = DeviceConfig(
            deviceId = "BLUETOOTH_A2DP_-**********",
            deviceName = "OPPO Enco Free4",
            deviceType = "BLUETOOTH_A2DP",
            lastUpdated = System.currentTimeMillis()
        )
        
        val configUpdateData = ConfigUpdateData(
            deviceId = "BLUETOOTH_A2DP_-**********",
            config = deviceConfig,
            version = 1,
            updatedBy = "other_device_456" // 不同的设备ID
        )
        
        val configUpdatedMessage = ConfigUpdatedMessage(
            data = configUpdateData
        )
        
        // Mock deviceConfigManager 返回成功
        coEvery { 
            deviceConfigManager.updateSingleDeviceConfig(any()) 
        } returns true
        
        // Mock getDeviceConfig 返回 null（表示没有现有配置）
        every { 
            deviceConfigManager.getDeviceConfig(any<String>()) 
        } returns null
        
        // 执行测试
        cloudSyncManager.onConfigUpdated(configUpdatedMessage)
        
        // 等待协程完成
        testDispatcher.advanceUntilIdle()
        
        // 验证：应该调用 updateSingleDeviceConfig
        coVerify(exactly = 1) { 
            deviceConfigManager.updateSingleDeviceConfig(deviceConfig) 
        }
    }

    @Test
    fun `onConfigUpdated should ignore older configurations`() = runBlockingTest {
        val currentTime = System.currentTimeMillis()
        
        // 现有配置（更新的）
        val existingConfig = DeviceConfig(
            deviceId = "BLUETOOTH_A2DP_-**********",
            deviceName = "OPPO Enco Free4",
            deviceType = "BLUETOOTH_A2DP",
            lastUpdated = currentTime
        )
        
        // 收到的配置（更旧的）
        val incomingConfig = DeviceConfig(
            deviceId = "BLUETOOTH_A2DP_-**********",
            deviceName = "OPPO Enco Free4",
            deviceType = "BLUETOOTH_A2DP",
            lastUpdated = currentTime - 1000 // 1秒前
        )
        
        val configUpdateData = ConfigUpdateData(
            deviceId = "BLUETOOTH_A2DP_-**********",
            config = incomingConfig,
            version = 1,
            updatedBy = "other_device_456"
        )
        
        val configUpdatedMessage = ConfigUpdatedMessage(
            data = configUpdateData
        )
        
        // Mock getDeviceConfig 返回现有配置
        every { 
            deviceConfigManager.getDeviceConfig("BLUETOOTH_A2DP_-**********") 
        } returns existingConfig
        
        // 执行测试
        cloudSyncManager.onConfigUpdated(configUpdatedMessage)
        
        // 等待协程完成
        testDispatcher.advanceUntilIdle()
        
        // 验证：不应该调用 updateSingleDeviceConfig（因为配置更旧）
        coVerify(exactly = 0) {
            deviceConfigManager.updateSingleDeviceConfig(any())
        }
    }

    @Test
    fun `onSyncFailed should handle not newer message gracefully`() = runBlockingTest {
        val syncFailedMessage = SyncFailedMessage(
            message = "Configuration is not newer than server version"
        )

        // 执行测试
        cloudSyncManager.onSyncFailed(syncFailedMessage)

        // 验证：状态应该设置为SYNCED（因为这是正常情况）
        assertEquals(CloudSyncStatus.SYNCED, cloudSyncManager.syncStatus.value)
    }

    @Test
    fun `onSyncFailed should handle other errors properly`() = runBlockingTest {
        val syncFailedMessage = SyncFailedMessage(
            message = "Network error occurred"
        )

        // 执行测试
        cloudSyncManager.onSyncFailed(syncFailedMessage)

        // 验证：状态应该设置为ERROR
        assertEquals(CloudSyncStatus.ERROR, cloudSyncManager.syncStatus.value)
    }
}
