package cn.ykload.flowmix.auth

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import cn.ykload.flowmix.data.AuthInfo
import cn.ykload.flowmix.data.LoginRequest
import cn.ykload.flowmix.network.FlowSyncApi
import cn.ykload.flowmix.network.NetworkManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext

/**
 * 用户认证管理器
 * 负责管理用户登录状态、authToken的存储和验证
 */
class AuthManager private constructor(
    private val context: Context,
    private val flowSyncApi: FlowSyncApi
) {

    companion object {
        private const val TAG = "AuthManager"
        private const val PREFS_NAME = "flowmix_auth"
        private const val KEY_AUTH_INFO = "auth_info"

        @Volatile
        private var INSTANCE: AuthManager? = null

        fun getInstance(context: Context, flowSyncApi: FlowSyncApi): AuthManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AuthManager(context.applicationContext, flowSyncApi).also { INSTANCE = it }
            }
        }
    }
    
    private val sharedPreferences: SharedPreferences =
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

    private val gson = NetworkManager.getGson()
    
    // 认证状态
    private val _authInfo = MutableStateFlow<AuthInfo?>(null)
    val authInfo: StateFlow<AuthInfo?> = _authInfo.asStateFlow()
    
    // 登录状态
    private val _isLoggedIn = MutableStateFlow(false)
    val isLoggedIn: StateFlow<Boolean> = _isLoggedIn.asStateFlow()
    
    init {
        loadAuthInfo()
    }
    
    /**
     * 使用登录码登录
     */
    suspend fun login(loginCode: String): LoginResult = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "尝试使用登录码登录: $loginCode")
            
            val request = LoginRequest(loginCode = loginCode)
            val response = flowSyncApi.login(request)
            
            if (response.isSuccessful) {
                val loginResponse = response.body()
                if (loginResponse?.success == true &&
                    loginResponse.authToken != null &&
                    loginResponse.qq != null) {

                    val authInfo = AuthInfo(
                        authToken = loginResponse.authToken,
                        qq = loginResponse.qq
                    )

                    saveAuthInfo(authInfo)
                    Log.d(TAG, "登录成功，QQ: ${authInfo.qq}")

                    LoginResult.Success(authInfo)
                } else {
                    val errorMsg = loginResponse?.message ?: "登录失败"
                    Log.w(TAG, "登录失败: $errorMsg, response: $loginResponse")
                    LoginResult.Error(errorMsg)
                }
            } else {
                val errorMsg = when (response.code()) {
                    403 -> "登录码无效或已过期"
                    else -> "网络请求失败: ${response.code()}"
                }
                Log.w(TAG, "登录请求失败: $errorMsg")
                LoginResult.Error(errorMsg)
            }
        } catch (e: Exception) {
            val errorMsg = "登录过程中发生错误: ${e.message}"
            Log.e(TAG, errorMsg, e)
            LoginResult.Error(errorMsg)
        }
    }
    
    /**
     * 自动登录（使用存储的authToken）
     */
    suspend fun autoLogin(): Boolean = withContext(Dispatchers.IO) {
        val authInfo = _authInfo.value
        if (authInfo != null) {
            Log.d(TAG, "使用存储的authToken自动登录")
            return@withContext true
        } else {
            Log.d(TAG, "没有存储的authToken，需要重新登录")
            return@withContext false
        }
    }
    
    /**
     * 登出
     */
    suspend fun logout() = withContext(Dispatchers.IO) {
        Log.d(TAG, "用户登出")
        clearAuthInfo()
    }
    
    /**
     * 获取当前有效的authToken
     */
    fun getCurrentAuthToken(): String? {
        return _authInfo.value?.authToken
    }

    /**
     * 获取当前用户QQ
     */
    fun getCurrentQQ(): String? {
        return _authInfo.value?.qq
    }
    
    /**
     * 保存认证信息
     */
    private fun saveAuthInfo(authInfo: AuthInfo) {
        try {
            val json = gson.toJson(authInfo)
            sharedPreferences.edit()
                .putString(KEY_AUTH_INFO, json)
                .apply()
            
            _authInfo.value = authInfo
            _isLoggedIn.value = true
            
            Log.d(TAG, "认证信息已保存")
        } catch (e: Exception) {
            Log.e(TAG, "保存认证信息失败", e)
        }
    }
    
    /**
     * 加载认证信息
     */
    private fun loadAuthInfo() {
        try {
            val json = sharedPreferences.getString(KEY_AUTH_INFO, null)
            if (json != null) {
                val authInfo = gson.fromJson(json, AuthInfo::class.java)
                if (authInfo != null) {
                    _authInfo.value = authInfo
                    _isLoggedIn.value = true
                    Log.d(TAG, "已加载认证信息，QQ: ${authInfo.qq}")
                } else {
                    Log.d(TAG, "认证信息格式无效")
                    clearAuthInfo()
                }
            } else {
                Log.d(TAG, "没有找到存储的认证信息")
            }
        } catch (e: Exception) {
            Log.e(TAG, "加载认证信息失败", e)
            clearAuthInfo()
        }
    }
    
    /**
     * 清除认证信息
     */
    private fun clearAuthInfo() {
        try {
            sharedPreferences.edit()
                .remove(KEY_AUTH_INFO)
                .apply()
            
            _authInfo.value = null
            _isLoggedIn.value = false
            
            Log.d(TAG, "认证信息已清除")
        } catch (e: Exception) {
            Log.e(TAG, "清除认证信息失败", e)
        }
    }
}

/**
 * 登录结果
 */
sealed class LoginResult {
    data class Success(val authInfo: AuthInfo) : LoginResult()
    data class Error(val message: String) : LoginResult()
}
