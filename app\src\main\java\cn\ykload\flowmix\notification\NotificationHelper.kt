package cn.ykload.flowmix.notification

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import cn.ykload.flowmix.MainActivity

/**
 * 通知管理工具类
 * 
 * 负责管理Flowmix应用的所有通知，包括：
 * 1. 保活服务的常驻通知
 * 2. 状态变更通知
 * 3. 错误提示通知
 */
class NotificationHelper(private val context: Context) {

    companion object {
        private const val TAG = "NotificationHelper"
        
        // 通知渠道ID
        const val CHANNEL_KEEP_ALIVE = "flowmix_keep_alive"

        // 通知ID
        const val NOTIFICATION_KEEP_ALIVE = 1001
        
        // 通知动作
        const val ACTION_OPEN_APP = "action_open_app"
    }

    private val notificationManager: NotificationManager by lazy {
        context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }

    init {
        createNotificationChannels()
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_KEEP_ALIVE,
                "Flowmix 保活服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "显示Flowmix运行状态的常驻通知"
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
                setSound(null, null)
            }

            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "通知渠道创建完成")
        }
    }

    /**
     * 创建保活服务通知
     */
    fun createKeepAliveNotification(isFlowmixEnabled: Boolean): Notification {
        val title = "Flowmix"
        val subtitle = if (isFlowmixEnabled) "On" else "Off"

        val iconRes = if (isFlowmixEnabled) {
            android.R.drawable.ic_media_play
        } else {
            android.R.drawable.ic_media_pause
        }

        // 点击通知打开应用
        val openAppIntent = createOpenAppIntent()

        return NotificationCompat.Builder(context, CHANNEL_KEEP_ALIVE)
            .setContentTitle(title)
            .setContentText(subtitle)
            .setSmallIcon(iconRes)
            .setContentIntent(openAppIntent)
            .setOngoing(true) // 常驻通知，用户无法清除
            .setAutoCancel(false)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()
    }

    /**
     * 更新保活通知
     */
    fun updateKeepAliveNotification(isFlowmixEnabled: Boolean) {
        val notification = createKeepAliveNotification(isFlowmixEnabled)
        notificationManager.notify(NOTIFICATION_KEEP_ALIVE, notification)
        Log.d(TAG, "保活通知已更新: ${if (isFlowmixEnabled) "On" else "Off"}")
    }

    /**
     * 取消指定通知
     */
    fun cancelNotification(notificationId: Int) {
        notificationManager.cancel(notificationId)
        Log.d(TAG, "通知已取消: $notificationId")
    }

    /**
     * 取消所有通知
     */
    fun cancelAllNotifications() {
        notificationManager.cancelAll()
        Log.d(TAG, "所有通知已取消")
    }

    /**
     * 创建打开应用的Intent
     */
    private fun createOpenAppIntent(): PendingIntent {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            action = ACTION_OPEN_APP
        }
        return PendingIntent.getActivity(
            context, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
    }



    /**
     * 检查通知权限
     */
    fun areNotificationsEnabled(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            notificationManager.areNotificationsEnabled()
        } else {
            true
        }
    }

    /**
     * 检查指定渠道是否启用
     */
    fun isChannelEnabled(channelId: String): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = notificationManager.getNotificationChannel(channelId)
            channel?.importance != NotificationManager.IMPORTANCE_NONE
        } else {
            true
        }
    }
}
