package cn.ykload.flowmix.ui.component

import androidx.compose.animation.core.EaseInOutCubic
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animate
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.input.pointer.changedToUp
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import cn.ykload.flowmix.data.AutoEqData
import cn.ykload.flowmix.data.MeasurementCondition
import cn.ykload.flowmix.ui.theme.createLexendTypeface
import kotlinx.coroutines.delay
import kotlin.math.abs
import kotlin.math.exp
import kotlin.math.ln
import kotlin.math.roundToInt

/**
 * 曲线信息数据类
 */
data class CurveInfo(
    val name: String,
    val spl: Float,
    val color: Color
)

/**
 * 频响曲线图组件
 */
@Composable
fun FrequencyResponseChart(
    measurementData: MeasurementCondition?,
    autoEqData: AutoEqData? = null,
    targetData: MeasurementCondition? = null,
    isLoudnessCompensationEnabled: Boolean = false,
    globalGain: Float = 0f,
    visibleBandRange: IntRange? = null,
    modifier: Modifier = Modifier,
    height: Dp = 220.dp,
    showChartLabel: Boolean = false,
    chartLabelText: String = "频响曲线",
    onPageEntered: Boolean = false,
    enableHeightAnimation: Boolean = false,
    // 曲线可见性参数
    isOriginalCurveVisible: Boolean = true,
    isAutoEqCurveVisible: Boolean = true,
    isTargetCurveVisible: Boolean = true,
    // 图例点击回调
    onOriginalCurveToggle: (() -> Unit)? = null,
    onAutoEqCurveToggle: (() -> Unit)? = null,
    onTargetCurveToggle: (() -> Unit)? = null,
    onClick: (() -> Unit)? = null
) {
    val density = LocalDensity.current
    val context = LocalContext.current

    // 使用与EQ图表相同的Material Design主题色
    val originalCurveColor = MaterialTheme.colorScheme.secondary  // 原始频响使用次要颜色
    val autoEqCurveColor = MaterialTheme.colorScheme.primary      // AutoEq调整后使用主要颜色（实际听感）
    val targetCurveColor = Color.White                            // 目标曲线使用白色（最低层级）
    val backgroundColor = MaterialTheme.colorScheme.surfaceVariant
    val gridColor = MaterialTheme.colorScheme.outline
    val textColor = MaterialTheme.colorScheme.onSurfaceVariant

    // 高度动画状态
    var heightAnimationProgress by remember { mutableStateOf(if (enableHeightAnimation) 0f else 1f) }

    // 图表标签动画状态
    var labelAnimationProgress by remember { mutableStateOf(0f) }
    var labelPositionProgress by remember { mutableStateOf(0f) }
    var labelScaleProgress by remember { mutableStateOf(0f) }

    // 当页面进入时触发高度和标签动画
    LaunchedEffect(onPageEntered) {
        if (onPageEntered) {
            // 高度动画：从0到完整高度
            if (enableHeightAnimation) {
                animate(
                    initialValue = 0f,
                    targetValue = 1f,
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) { value, _ ->
                    heightAnimationProgress = value
                }
            }

            // 标签动画
            if (showChartLabel) {
                // 第一阶段：标签在中央以大字号显示
                animate(
                    initialValue = 0f,
                    targetValue = 1f,
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioMediumBouncy,
                        stiffness = Spring.StiffnessMedium
                    )
                ) { value, _ ->
                    labelScaleProgress = value
                }

                delay(800) // 在中央停留0.8秒

                // 第二阶段：移动到右上角并缩小
                animate(
                    initialValue = 0f,
                    targetValue = 1f,
                    animationSpec = tween(
                        durationMillis = 600,
                        easing = EaseInOutCubic
                    )
                ) { value, _ ->
                    labelPositionProgress = value
                    labelAnimationProgress = value
                }
            }
        }
    }
    
    // 长按拖拽状态
    var dragPosition by remember { mutableStateOf<Offset?>(null) }
    var isDragging by remember { mutableStateOf(false) }
    var isLongPressActivated by remember { mutableStateOf(false) }

    // 自动重置长按状态
    LaunchedEffect(isLongPressActivated) {
        if (isLongPressActivated) {
            delay(5000) // 5秒后自动重置
            if (isLongPressActivated) {
                isDragging = false
                dragPosition = null
                isLongPressActivated = false
            }
        }
    }

    // 频响数据变化动画
    var displayMeasurementData by remember { mutableStateOf(measurementData) }
    var targetMeasurementData by remember { mutableStateOf(measurementData) }
    var displayTargetData by remember { mutableStateOf(targetData) }
    var targetTargetData by remember { mutableStateOf(targetData) }
    var dataAnimationProgress by remember { mutableStateOf(1f) }
    var isDataAnimating by remember { mutableStateOf(false) }

    // 当测量数据变化时触发动画
    LaunchedEffect(measurementData) {
        if (measurementData != displayMeasurementData) {
            targetMeasurementData = measurementData
            isDataAnimating = true
            dataAnimationProgress = 0f

            // 启动动画
            animate(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = 1000, // 1秒动画
                    easing = EaseInOutCubic
                )
            ) { value, _ ->
                dataAnimationProgress = value
            }

            // 动画完成后更新显示数据 - 原子性更新避免闪现
            displayMeasurementData = targetMeasurementData
            dataAnimationProgress = 1f
            isDataAnimating = false
        } else if (!isDataAnimating) {
            displayMeasurementData = measurementData
            targetMeasurementData = measurementData
            dataAnimationProgress = 1f
        }
    }

    // 当目标曲线数据变化时触发动画
    LaunchedEffect(targetData) {
        if (targetData != displayTargetData) {
            targetTargetData = targetData
            isDataAnimating = true
            dataAnimationProgress = 0f

            // 启动动画
            animate(
                initialValue = 0f,
                targetValue = 1f,
                animationSpec = tween(
                    durationMillis = 1000, // 1秒动画
                    easing = EaseInOutCubic
                )
            ) { value, _ ->
                dataAnimationProgress = value
            }

            // 动画完成后更新显示数据 - 原子性更新避免闪现
            displayTargetData = targetTargetData
            dataAnimationProgress = 1f
            isDataAnimating = false
        } else if (!isDataAnimating) {
            displayTargetData = targetData
            targetTargetData = targetData
            dataAnimationProgress = 1f
        }
    }

    // 等响度补偿动画状态
    val compensationValue = remember(autoEqData) {
        autoEqData?.calculateLoudnessCompensation() ?: 0f
    }

    val compensatedAutoEqData = remember(autoEqData, compensationValue) {
        autoEqData?.withLoudnessCompensation(compensationValue)
    }

    // 计算整体增益调节后的AutoEq数据
    val globalGainAutoEqData = remember(autoEqData, globalGain) {
        autoEqData?.withGlobalGain(globalGain)
    }

    // 确定目标增益值：等响度补偿优先，否则使用整体增益
    val targetGainValue = if (isLoudnessCompensationEnabled) compensationValue else globalGain
    val hasGainAdjustment = isLoudnessCompensationEnabled || globalGain != 0f

    // 增益调节动画进度
    val gainAdjustmentAnimationProgress by animateFloatAsState(
        targetValue = if (hasGainAdjustment) 1f else 0f,
        animationSpec = tween(
            durationMillis = 1000,
            easing = FastOutSlowInEasing
        ),
        label = "gain_adjustment_animation"
    )

    // 计算动画高度
    val animatedHeight by animateDpAsState(
        targetValue = height * heightAnimationProgress,
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioMediumBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "chart_height_animation"
    )

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(if (enableHeightAnimation) animatedHeight else height)
            .clip(RoundedCornerShape(25.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        backgroundColor,
                        backgroundColor.copy(alpha = 0.8f)
                    )
                )
            )
    ) {
        Canvas(
            modifier = Modifier
                .fillMaxSize()
                .pointerInput(Unit) {
                    detectTapGestures(
                        onLongPress = { offset ->
                            isLongPressActivated = true
                            isDragging = true
                            dragPosition = offset
                        },
                        onTap = {
                            // 点击时重置状态
                            if (isLongPressActivated) {
                                isDragging = false
                                dragPosition = null
                                isLongPressActivated = false
                            } else {
                                // 如果不是长按状态，执行点击回调
                                onClick?.invoke()
                            }
                        }
                    )
                }
                .pointerInput(Unit) {
                    // 处理长按后的拖拽
                    awaitPointerEventScope {
                        while (true) {
                            val event = awaitPointerEvent()
                            if (isLongPressActivated) {
                                event.changes.forEach { change ->
                                    if (change.pressed) {
                                        // 更新拖拽位置
                                        dragPosition = change.position
                                        change.consume()
                                    } else if (change.changedToUp()) {
                                        // 手指抬起，结束拖拽
                                        isDragging = false
                                        dragPosition = null
                                        isLongPressActivated = false
                                        change.consume()
                                    }
                                }
                            }
                        }
                    }
                }
        ) {
            drawFrequencyResponseChart(
                measurementData = measurementData,
                displayMeasurementData = displayMeasurementData,
                targetMeasurementData = targetMeasurementData,
                autoEqData = autoEqData,
                compensatedAutoEqData = compensatedAutoEqData,
                globalGainAutoEqData = globalGainAutoEqData,
                targetData = targetData,
                displayTargetData = displayTargetData,
                targetTargetData = targetTargetData,
                isLoudnessCompensationEnabled = isLoudnessCompensationEnabled,
                gainAdjustmentAnimationProgress = gainAdjustmentAnimationProgress,
                dataAnimationProgress = dataAnimationProgress,
                isDataAnimating = isDataAnimating,
                originalCurveColor = originalCurveColor,
                autoEqCurveColor = autoEqCurveColor,
                targetCurveColor = targetCurveColor,
                gridColor = gridColor,
                textColor = textColor,
                backgroundColor = backgroundColor,
                dragPosition = dragPosition,
                isDragging = isDragging,
                visibleBandRange = visibleBandRange,
                // 曲线可见性参数
                isOriginalCurveVisible = isOriginalCurveVisible,
                isAutoEqCurveVisible = isAutoEqCurveVisible,
                isTargetCurveVisible = isTargetCurveVisible,
                density = density,
                context = context
            )

            // 绘制图表标签
            if (showChartLabel) {
                drawFrequencyResponseChartLabel(
                    labelText = chartLabelText,
                    labelScaleProgress = labelScaleProgress,
                    labelPositionProgress = labelPositionProgress,
                    labelAnimationProgress = labelAnimationProgress,
                    textColor = textColor,
                    backgroundColor = backgroundColor,
                    density = density,
                    context = context
                )
            }
        }
    }
}

/**
 * 绘制频响图表
 */
private fun DrawScope.drawFrequencyResponseChart(
    measurementData: MeasurementCondition?,
    displayMeasurementData: MeasurementCondition?,
    targetMeasurementData: MeasurementCondition?,
    autoEqData: AutoEqData?,
    compensatedAutoEqData: AutoEqData?,
    globalGainAutoEqData: AutoEqData?,
    targetData: MeasurementCondition?,
    displayTargetData: MeasurementCondition?,
    targetTargetData: MeasurementCondition?,
    isLoudnessCompensationEnabled: Boolean,
    gainAdjustmentAnimationProgress: Float,
    dataAnimationProgress: Float,
    isDataAnimating: Boolean,
    originalCurveColor: Color,
    autoEqCurveColor: Color,
    targetCurveColor: Color,
    gridColor: Color,
    textColor: Color,
    backgroundColor: Color,
    dragPosition: Offset?,
    isDragging: Boolean,
    visibleBandRange: IntRange?,
    // 曲线可见性参数
    isOriginalCurveVisible: Boolean,
    isAutoEqCurveVisible: Boolean,
    isTargetCurveVisible: Boolean,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    if (measurementData == null) return
    
    val chartWidth = size.width
    val chartHeight = size.height
    val chartLeft = 0f
    val chartTop = 0f
    
    // 计算自适应的SPL范围
    val splRange = calculateAdaptiveSplRange(measurementData)
    val minSpl = splRange.first
    val maxSpl = splRange.second
    
    // 频率范围 (20-20000 Hz，对数刻度)
    val minFreq = 20f
    val maxFreq = 20000f
    
    // 绘制网格线
    drawFrequencyResponseGrid(
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        minSpl = minSpl,
        maxSpl = maxSpl,
        minFreq = minFreq,
        maxFreq = maxFreq,
        gridColor = gridColor
    )
    
    // 绘制标签
    drawFrequencyResponseLabels(
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        minSpl = minSpl,
        maxSpl = maxSpl,
        minFreq = minFreq,
        maxFreq = maxFreq,
        textColor = textColor,
        density = density,
        context = context
    )
    
    // 绘制目标曲线（最底层，白色线条）- 支持动画
    val effectiveTargetData = if (isDataAnimating && dataAnimationProgress < 1f) {
        // 动画中：在displayTargetData和targetTargetData之间插值
        interpolateTargetData(displayTargetData, targetTargetData, dataAnimationProgress)
    } else if (dataAnimationProgress >= 1f && targetTargetData != null) {
        // 动画完成或接近完成，确保使用目标数据
        targetTargetData
    } else {
        displayTargetData
    }

    // 绘制目标曲线（最底层，白色线条）- 支持动画和可见性控制
    if (isTargetCurveVisible && effectiveTargetData != null && effectiveTargetData.isValid()) {
        drawFrequencyResponseCurve(
            frequencies = effectiveTargetData.getValidFrequencies(),
            splValues = effectiveTargetData.getValidSplValues(),
            chartLeft = chartLeft,
            chartTop = chartTop,
            chartWidth = chartWidth,
            chartHeight = chartHeight,
            minSpl = minSpl,
            maxSpl = maxSpl,
            minFreq = minFreq,
            maxFreq = maxFreq,
            curveColor = targetCurveColor,
            showFill = false, // 目标曲线不显示填充
            strokeWidth = 2.dp.toPx() // 统一使用2dp线条
        )
    }

    // 绘制原始频响曲线 - 支持动画
    val effectiveMeasurementData = if (isDataAnimating && dataAnimationProgress < 1f) {
        // 动画中：在displayMeasurementData和targetMeasurementData之间插值
        interpolateMeasurementData(displayMeasurementData, targetMeasurementData, dataAnimationProgress)
    } else if (dataAnimationProgress >= 1f && targetMeasurementData != null) {
        // 动画完成或接近完成，确保使用目标数据
        targetMeasurementData
    } else {
        displayMeasurementData
    }

    // 绘制原始频响曲线 - 支持动画和可见性控制
    if (isOriginalCurveVisible && effectiveMeasurementData != null && effectiveMeasurementData.isValid()) {
        drawFrequencyResponseCurve(
            frequencies = effectiveMeasurementData.getValidFrequencies(),
            splValues = effectiveMeasurementData.getValidSplValues(),
            chartLeft = chartLeft,
            chartTop = chartTop,
            chartWidth = chartWidth,
            chartHeight = chartHeight,
            minSpl = minSpl,
            maxSpl = maxSpl,
            minFreq = minFreq,
            maxFreq = maxFreq,
            curveColor = originalCurveColor,
            showFill = autoEqData == null, // 只有在没有AutoEq时才显示填充，目标曲线不影响
            strokeWidth = 2.dp.toPx() // 统一使用2dp线条
        )
    }

    // 绘制AutoEq调整后的频响曲线（支持等响度补偿动画和可见性控制）
    if (isAutoEqCurveVisible && autoEqData != null && effectiveMeasurementData != null && effectiveMeasurementData.isValid()) {
        // 计算原始和补偿后的SPL值（使用插值后的测量数据）
        val originalAdjustedSplValues = calculateAdjustedSplValues(
            originalSplValues = effectiveMeasurementData.getValidSplValues(),
            frequencies = effectiveMeasurementData.getValidFrequencies(),
            autoEqData = autoEqData
        )

        val compensatedAdjustedSplValues = if (compensatedAutoEqData != null) {
            calculateAdjustedSplValues(
                originalSplValues = effectiveMeasurementData.getValidSplValues(),
                frequencies = effectiveMeasurementData.getValidFrequencies(),
                autoEqData = compensatedAutoEqData
            )
        } else {
            originalAdjustedSplValues
        }

        // 确定目标调节后的SPL值
        val targetAdjustedSplValues = if (isLoudnessCompensationEnabled) {
            compensatedAdjustedSplValues
        } else if (globalGainAutoEqData != null) {
            calculateAdjustedSplValues(
                originalSplValues = measurementData.getValidSplValues(),
                frequencies = measurementData.getValidFrequencies(),
                autoEqData = globalGainAutoEqData
            )
        } else {
            originalAdjustedSplValues
        }

        if (originalAdjustedSplValues.isNotEmpty() && targetAdjustedSplValues.isNotEmpty()) {
            // 计算动画插值的SPL值
            val animatedSplValues = originalAdjustedSplValues.mapIndexed { index, originalSpl ->
                val targetSpl = targetAdjustedSplValues.getOrElse(index) { originalSpl }
                originalSpl + (targetSpl - originalSpl) * gainAdjustmentAnimationProgress
            }

            // 绘制背景曲线（当有动画时变暗）
            val backgroundAlpha = if (gainAdjustmentAnimationProgress > 0f) 0.3f else 1f
            drawFrequencyResponseCurve(
                frequencies = measurementData.getValidFrequencies(),
                splValues = originalAdjustedSplValues,
                chartLeft = chartLeft,
                chartTop = chartTop,
                chartWidth = chartWidth,
                chartHeight = chartHeight,
                minSpl = minSpl,
                maxSpl = maxSpl,
                minFreq = minFreq,
                maxFreq = maxFreq,
                curveColor = autoEqCurveColor.copy(alpha = backgroundAlpha),
                showFill = gainAdjustmentAnimationProgress == 0f,
                strokeWidth = 2.dp.toPx()
            )

            // 绘制动画插值曲线
            if (gainAdjustmentAnimationProgress > 0f) {
                drawFrequencyResponseCurve(
                    frequencies = measurementData.getValidFrequencies(),
                    splValues = animatedSplValues,
                    chartLeft = chartLeft,
                    chartTop = chartTop,
                    chartWidth = chartWidth,
                    chartHeight = chartHeight,
                    minSpl = minSpl,
                    maxSpl = maxSpl,
                    minFreq = minFreq,
                    maxFreq = maxFreq,
                    curveColor = autoEqCurveColor,
                    showFill = true,
                    strokeWidth = 2.dp.toPx()
                )
            }
        }
    }

    // 绘制可见频段高亮 - 只在有AutoEq数据且有可见范围时显示
    if (visibleBandRange != null && autoEqData != null && autoEqData.bands.isNotEmpty()) {
        drawVisibleBandHighlight(
            visibleBandRange = visibleBandRange,
            eqBands = autoEqData.bands,
            chartLeft = chartLeft,
            chartTop = chartTop,
            chartWidth = chartWidth,
            chartHeight = chartHeight,
            minFreq = minFreq,
            maxFreq = maxFreq,
            curveColor = autoEqCurveColor
        )
    }

    // 绘制拖拽指示器
    if (isDragging && dragPosition != null && effectiveMeasurementData != null && effectiveMeasurementData.isValid()) {
        // 计算AutoEq调整后的SPL值（考虑增益调节）
        val adjustedSplValues = if (autoEqData != null) {
            val effectiveAutoEqData = if (gainAdjustmentAnimationProgress > 0f) {
                if (isLoudnessCompensationEnabled && compensatedAutoEqData != null) {
                    compensatedAutoEqData
                } else if (globalGainAutoEqData != null) {
                    globalGainAutoEqData
                } else {
                    autoEqData
                }
            } else {
                autoEqData
            }
            calculateAdjustedSplValues(
                originalSplValues = effectiveMeasurementData.getValidSplValues(),
                frequencies = effectiveMeasurementData.getValidFrequencies(),
                autoEqData = effectiveAutoEqData
            )
        } else null

        drawFrequencyResponseDragIndicator(
            dragPosition = dragPosition,
            chartLeft = chartLeft,
            chartTop = chartTop,
            chartWidth = chartWidth,
            chartHeight = chartHeight,
            minSpl = minSpl,
            maxSpl = maxSpl,
            minFreq = minFreq,
            maxFreq = maxFreq,
            measurementData = effectiveMeasurementData,
            autoEqData = autoEqData,
            adjustedSplValues = adjustedSplValues,
            targetData = effectiveTargetData,
            originalCurveColor = originalCurveColor,
            autoEqCurveColor = autoEqCurveColor,
            targetCurveColor = targetCurveColor,
            textColor = textColor,
            backgroundColor = backgroundColor,
            density = density,
            context = context
        )
    }
}

/**
 * 计算AutoEq调整后的SPL值
 */
private fun calculateAdjustedSplValues(
    originalSplValues: List<Float>,
    frequencies: List<Float>,
    autoEqData: AutoEqData
): List<Float> {
    if (originalSplValues.isEmpty() || frequencies.isEmpty() ||
        originalSplValues.size != frequencies.size) {
        return originalSplValues
    }

    return originalSplValues.mapIndexed { index, originalSpl ->
        if (index < frequencies.size && originalSpl.isFinite() && !originalSpl.isNaN() && frequencies[index] > 0f) {
            val frequency = frequencies[index]
            // 找到对应频率的EQ增益
            val eqGain = findGainAtFrequency(autoEqData.bands, frequency)
            originalSpl + eqGain
        } else {
            originalSpl
        }
    }
}

/**
 * 在指定频率处查找增益值
 */
private fun findGainAtFrequency(bands: List<cn.ykload.flowmix.data.EqBand>, targetFrequency: Float): Float {
    if (bands.isEmpty()) return 0f

    // 找到最接近的频段
    val closestBand = bands.minByOrNull { abs(it.frequency - targetFrequency) }
    return closestBand?.gain ?: 0f
}

/**
 * 绘制频响网格线
 */
private fun DrawScope.drawFrequencyResponseGrid(
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minSpl: Float,
    maxSpl: Float,
    minFreq: Float,
    maxFreq: Float,
    gridColor: Color
) {
    val lightGridColor = gridColor.copy(alpha = 0.1f)
    val mediumGridColor = gridColor.copy(alpha = 0.2f)

    // 水平网格线 (SPL) - 每5dB一条线，但不显示最大和最小边界线
    val splStep = 5f
    var spl = (minSpl / splStep).roundToInt() * splStep
    while (spl <= maxSpl) {
        if (spl >= minSpl && spl <= maxSpl && spl != minSpl && spl != maxSpl) {
            val y = chartTop + chartHeight * (1f - (spl - minSpl) / (maxSpl - minSpl))
            val color = if (spl % 10 == 0f) mediumGridColor else lightGridColor
            val strokeWidth = if (spl % 10 == 0f) 1.dp.toPx() else 0.5.dp.toPx()

            drawLine(
                color = color,
                start = Offset(chartLeft, y),
                end = Offset(chartLeft + chartWidth, y),
                strokeWidth = strokeWidth
            )
        }
        spl += splStep
    }

    // 垂直网格线 (频率) - 不显示20和20k的垂直线
    val logMinFreq = ln(minFreq)
    val logMaxFreq = ln(maxFreq)

    // 主要频率点 - 不包括20和20k
    val majorFrequencies = listOf(100f, 500f, 1000f, 3000f, 10000f)
    majorFrequencies.forEach { freq ->
        if (freq >= minFreq && freq <= maxFreq) {
            val logFreq = ln(freq)
            val x = chartLeft + chartWidth * (logFreq - logMinFreq) / (logMaxFreq - logMinFreq)

            drawLine(
                color = mediumGridColor,
                start = Offset(x, chartTop),
                end = Offset(x, chartTop + chartHeight),
                strokeWidth = 1.dp.toPx()
            )
        }
    }
}

/**
 * 绘制频响标签
 */
private fun DrawScope.drawFrequencyResponseLabels(
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minSpl: Float,
    maxSpl: Float,
    minFreq: Float,
    maxFreq: Float,
    textColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    val lexendTypeface = createLexendTypeface(context)
    val textPaint = android.graphics.Paint().apply {
        color = textColor.toArgb()
        textSize = with(density) { 10.sp.toPx() }
        typeface = lexendTypeface
        isAntiAlias = true
    }

    // SPL标签 - 每10dB显示一个标签，显示在图表左侧水平线上方，但不显示最大和最小边界标签
    val splStep = 10f
    var spl = (minSpl / splStep).roundToInt() * splStep
    while (spl <= maxSpl) {
        if (spl >= minSpl && spl <= maxSpl && spl != minSpl && spl != maxSpl) {
            val y = chartTop + chartHeight * (1f - (spl - minSpl) / (maxSpl - minSpl))
            val text = "${spl.toInt()}"

            // 显示在图表左侧，水平线上方
            drawContext.canvas.nativeCanvas.drawText(
                text,
                chartLeft + 8.dp.toPx(), // 显示在左侧
                y - 4.dp.toPx(), // 显示在线的上方
                textPaint
            )
        }
        spl += splStep
    }

    // 频率标签 (底部) - 只显示指定的频率点
    val logMinFreq = ln(minFreq)
    val logMaxFreq = ln(maxFreq)
    val labelFrequencies = listOf(20f, 100f, 500f, 1000f, 3000f, 10000f, 20000f)

    labelFrequencies.forEach { freq ->
        if (freq >= minFreq && freq <= maxFreq) {
            val logFreq = ln(freq)
            val x = chartLeft + chartWidth * (logFreq - logMinFreq) / (logMaxFreq - logMinFreq)

            val text = when {
                freq >= 1000f -> "${(freq / 1000f).toInt()}k"
                else -> freq.toInt().toString()
            }

            val textWidth = textPaint.measureText(text)

            // 特殊处理20Hz和20kHz的位置
            val textX = when (freq) {
                20f -> (x + 12.dp.toPx()).coerceAtMost(chartLeft + chartWidth - textWidth) // 20标在右侧，但不超出边界
                20000f -> (x - textWidth - 6.dp.toPx()).coerceAtLeast(chartLeft) // 20k标在左侧，确保不超出左边界
                else -> x - textWidth / 2 // 其他频率居中
            }

            drawContext.canvas.nativeCanvas.drawText(
                text,
                textX,
                chartTop + chartHeight - 8.dp.toPx(),
                textPaint
            )
        }
    }
}

/**
 * 绘制频响曲线
 */
private fun DrawScope.drawFrequencyResponseCurve(
    frequencies: List<Float>,
    splValues: List<Float>,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minSpl: Float,
    maxSpl: Float,
    minFreq: Float,
    maxFreq: Float,
    curveColor: Color,
    showFill: Boolean = true,
    strokeWidth: Float = 2.dp.toPx()
) {
    // 添加空值检查
    if (frequencies.isEmpty() || splValues.isEmpty() ||
        frequencies.size != splValues.size || frequencies.size < 2) return

    val path = Path()
    val fillPath = Path()

    val logMinFreq = ln(minFreq)
    val logMaxFreq = ln(maxFreq)

    // 过滤掉无效的数据点（频率为0或无效的SPL值）
    val validData = frequencies.zip(splValues) { freq, spl ->
        Pair(freq, spl)
    }.filter { (freq, spl) ->
        freq > 0f && spl.isFinite() && !spl.isNaN() && freq >= minFreq && freq <= maxFreq
    }

    if (validData.size < 2) return

    // 计算点的坐标
    val points = validData.map { (freq, spl) ->
        val logFreq = ln(freq)
        val x = chartLeft + chartWidth * (logFreq - logMinFreq) / (logMaxFreq - logMinFreq)
        val y = chartTop + chartHeight * (1f - (spl - minSpl) / (maxSpl - minSpl))
        Offset(x, y)
    }

    // 创建平滑曲线
    if (points.isNotEmpty()) {
        path.moveTo(points[0].x, points[0].y)
        fillPath.moveTo(points[0].x, points[0].y)

        // 使用Catmull-Rom样条曲线创建更自然的曲线
        if (points.size > 2) {
            for (i in 1 until points.size) {
                val p0 = if (i > 1) points[i - 2] else points[i - 1]
                val p1 = points[i - 1]
                val p2 = points[i]
                val p3 = if (i < points.size - 1) points[i + 1] else points[i]

                // 计算控制点
                val tension = 0.4f
                val control1X = p1.x + (p2.x - p0.x) * tension / 6f
                val control1Y = p1.y + (p2.y - p0.y) * tension / 6f
                val control2X = p2.x - (p3.x - p1.x) * tension / 6f
                val control2Y = p2.y - (p3.y - p1.y) * tension / 6f

                path.cubicTo(control1X, control1Y, control2X, control2Y, p2.x, p2.y)
                fillPath.cubicTo(control1X, control1Y, control2X, control2Y, p2.x, p2.y)
            }
        } else {
            // 如果点数较少，使用直线连接
            for (i in 1 until points.size) {
                path.lineTo(points[i].x, points[i].y)
                fillPath.lineTo(points[i].x, points[i].y)
            }
        }

        // 绘制填充区域（如果启用）
        if (showFill) {
            // 创建填充区域 - 从曲线到图表底部
            fillPath.lineTo(points.last().x, chartTop + chartHeight)
            fillPath.lineTo(points.first().x, chartTop + chartHeight)
            fillPath.close()

            // 绘制渐变填充区域
            val gradient = Brush.verticalGradient(
                colors = listOf(
                    curveColor.copy(alpha = 0.25f),   // 曲线处较浓
                    curveColor.copy(alpha = 0.12f),   // 中间
                    curveColor.copy(alpha = 0.04f),   // 底部很淡
                    Color.Transparent                 // 完全透明
                ),
                startY = chartTop,
                endY = chartTop + chartHeight
            )

            drawPath(
                path = fillPath,
                brush = gradient
            )
        }

        // 绘制曲线
        drawPath(
            path = path,
            color = curveColor,
            style = Stroke(
                width = strokeWidth,
                cap = StrokeCap.Round,
                join = StrokeJoin.Round
            )
        )
    }
}

/**
 * 绘制拖拽指示器
 */
private fun DrawScope.drawFrequencyResponseDragIndicator(
    dragPosition: Offset,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minSpl: Float,
    maxSpl: Float,
    minFreq: Float,
    maxFreq: Float,
    measurementData: MeasurementCondition,
    autoEqData: AutoEqData?,
    adjustedSplValues: List<Float>?,
    targetData: MeasurementCondition?,
    originalCurveColor: Color,
    autoEqCurveColor: Color,
    targetCurveColor: Color,
    textColor: Color,
    backgroundColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    val x = dragPosition.x

    // 确保拖拽位置在图表范围内
    if (x < chartLeft || x > chartLeft + chartWidth) {
        return
    }

    // 计算对应的频率
    val logMinFreq = ln(minFreq)
    val logMaxFreq = ln(maxFreq)
    val logFreq = logMinFreq + (x - chartLeft) / chartWidth * (logMaxFreq - logMinFreq)
    val frequency = exp(logFreq).coerceIn(minFreq, maxFreq)

    // 收集所有曲线的数据
    val curveData = mutableListOf<CurveInfo>()

    // 目标曲线（如果存在）
    if (targetData != null && targetData.isValid()) {
        val targetSpl = findSplAtFrequency(targetData.getValidFrequencies(), targetData.getValidSplValues(), frequency)
        curveData.add(CurveInfo("目标曲线", targetSpl, targetCurveColor))
    }

    // 原始频响曲线
    val originalSpl = findSplAtFrequency(measurementData.getValidFrequencies(), measurementData.getValidSplValues(), frequency)
    curveData.add(CurveInfo("原始频响", originalSpl, originalCurveColor))

    // AutoEq调整后曲线（如果存在）
    if (autoEqData != null && adjustedSplValues != null && adjustedSplValues.isNotEmpty()) {
        val autoEqSpl = findSplAtFrequency(measurementData.getValidFrequencies(), adjustedSplValues, frequency)
        curveData.add(CurveInfo("AutoEq调整后", autoEqSpl, autoEqCurveColor))
    }

    // 收集所有曲线的Y坐标，用于确定垂直线的范围
    val curveYPositions = curveData.map { curve ->
        chartTop + chartHeight * (1f - (curve.spl - minSpl) / (maxSpl - minSpl))
    }

    // 绘制垂直指示线 - 只绘制到最上方的点，使用更淡的颜色
    val indicatorColor = textColor.copy(alpha = 0.4f) 
    val topY = curveYPositions.minOrNull() ?: chartTop
    drawLine(
        color = indicatorColor,
        start = Offset(x, topY),
        end = Offset(x, chartTop + chartHeight),
        strokeWidth = 1.dp.toPx()
    )

    // 绘制每条曲线的交叉点
    curveData.forEach { curve ->
        val curveY = chartTop + chartHeight * (1f - (curve.spl - minSpl) / (maxSpl - minSpl))

        // 绘制水平指示线 - 使用更淡的颜色
        drawLine(
            color = curve.color.copy(alpha = 0.4f), 
            start = Offset(chartLeft, curveY),
            end = Offset(x, curveY),
            strokeWidth = 1.dp.toPx()
        )

        // 绘制交叉点圆圈
        drawCircle(
            color = curve.color,
            radius = 2.5.dp.toPx(),
            center = Offset(x, curveY),
            style = Stroke(width = 2.dp.toPx())
        )

        // 内部填充圆圈 - 使用背景色
        drawCircle(
            color = backgroundColor,
            radius = 2.5.dp.toPx(),
            center = Offset(x, curveY)
        )
    }

    // 计算最佳标签位置（避让曲线）
    val optimalLabelPosition = calculateOptimalLabelPosition(
        curveData = curveData,
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        minSpl = minSpl,
        maxSpl = maxSpl,
        dragX = x,
        density = density
    )

    // 绘制信息标签
    drawFrequencyResponseInfoLabel(
        frequency = frequency,
        curveData = curveData,
        position = optimalLabelPosition,
        chartLeft = chartLeft,
        chartTop = chartTop,
        chartWidth = chartWidth,
        chartHeight = chartHeight,
        textColor = textColor,
        backgroundColor = backgroundColor,
        density = density,
        context = context
    )
}

/**
 * 计算最佳标签位置以避让曲线
 */
private fun calculateOptimalLabelPosition(
    curveData: List<CurveInfo>,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minSpl: Float,
    maxSpl: Float,
    dragX: Float,
    density: androidx.compose.ui.unit.Density
): Offset {
    if (curveData.isEmpty()) {
        return Offset(dragX, chartTop + chartHeight * 0.5f)
    }

    // 标签尺寸
    val labelHeight = with(density) { 8.dp.toPx() * 2 + 16.dp.toPx() * (curveData.size + 1) } // padding + lines
    val labelWidth = with(density) { 140.dp.toPx() }
    val margin = with(density) { 16.dp.toPx() }

    // 获取所有曲线在当前X位置的Y坐标
    val curveYPositions = curveData.map { curve ->
        chartTop + chartHeight * (1f - (curve.spl - minSpl) / (maxSpl - minSpl))
    }.sorted()

    // 寻找最大的空隙
    val gaps = mutableListOf<Pair<Float, Float>>() // (start, end)

    // 顶部空隙
    if (curveYPositions.first() > chartTop + labelHeight + margin) {
        gaps.add(Pair(chartTop, curveYPositions.first() - margin))
    }

    // 曲线之间的空隙
    for (i in 0 until curveYPositions.size - 1) {
        val gapStart = curveYPositions[i] + margin
        val gapEnd = curveYPositions[i + 1] - margin
        if (gapEnd - gapStart >= labelHeight) {
            gaps.add(Pair(gapStart, gapEnd))
        }
    }

    // 底部空隙
    if (curveYPositions.last() < chartTop + chartHeight - labelHeight - margin) {
        gaps.add(Pair(curveYPositions.last() + margin, chartTop + chartHeight))
    }

    // 选择最大的空隙
    val bestGap = gaps.maxByOrNull { it.second - it.first }

    val labelY = if (bestGap != null) {
        // 在最大空隙中居中
        bestGap.first + (bestGap.second - bestGap.first) / 2
    } else {
        // 如果没有合适的空隙，选择距离曲线最远的位置
        val topDistance = curveYPositions.minOf { it - chartTop }
        val bottomDistance = curveYPositions.minOf { chartTop + chartHeight - it }

        if (topDistance > bottomDistance) {
            chartTop + labelHeight / 2 + margin
        } else {
            chartTop + chartHeight - labelHeight / 2 - margin
        }
    }

    return Offset(dragX, labelY.coerceIn(chartTop + labelHeight / 2, chartTop + chartHeight - labelHeight / 2))
}

/**
 * 绘制频响信息标签 - 显示所有曲线的值
 */
private fun DrawScope.drawFrequencyResponseInfoLabel(
    frequency: Float,
    curveData: List<CurveInfo>,
    position: Offset,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    textColor: Color,
    backgroundColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    val freqText = when {
        frequency < 1000 -> "${frequency.toInt()}Hz"
        else -> "${(frequency / 1000).let { if (it == it.toInt().toFloat()) it.toInt().toString() else "%.1f".format(it) }}kHz"
    }

    // 计算标签尺寸 - 根据曲线数量调整
    val lineHeight = 16.dp.toPx()
    val padding = 8.dp.toPx()
    val labelWidth = 140.dp.toPx()
    val labelHeight = padding * 2 + lineHeight * (curveData.size + 1) // +1 for frequency line

    var labelX = position.x - labelWidth / 2
    var labelY = position.y - labelHeight / 2 // 使用传入的position作为中心点

    // 调整X位置避免超出边界
    if (labelX < chartLeft) labelX = chartLeft + 8.dp.toPx()
    if (labelX + labelWidth > chartLeft + chartWidth) labelX = chartLeft + chartWidth - labelWidth - 8.dp.toPx()

    // 确保Y位置在图表范围内
    labelY = labelY.coerceIn(chartTop + 8.dp.toPx(), chartTop + chartHeight - labelHeight - 8.dp.toPx())

    // 绘制标签背景 - 使用Material Design的surface颜色
    drawRoundRect(
        color = backgroundColor.copy(alpha = 0.95f),
        topLeft = Offset(labelX, labelY),
        size = androidx.compose.ui.geometry.Size(labelWidth, labelHeight),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(25.dp.toPx()),
        style = androidx.compose.ui.graphics.drawscope.Fill
    )

    // 绘制标签边框
    drawRoundRect(
        color = textColor.copy(alpha = 0.2f),
        topLeft = Offset(labelX, labelY),
        size = androidx.compose.ui.geometry.Size(labelWidth, labelHeight),
        cornerRadius = androidx.compose.ui.geometry.CornerRadius(25.dp.toPx()),
        style = Stroke(width = 1.dp.toPx())
    )

    // 绘制文字
    drawContext.canvas.nativeCanvas.apply {
        val lexendTypeface = createLexendTypeface(context)
        val textPaint = android.graphics.Paint().apply {
            color = textColor.toArgb()
            textSize = with(density) { 11.sp.toPx() }
            typeface = lexendTypeface
            isAntiAlias = true
        }

        val valuePaint = android.graphics.Paint().apply {
            color = textColor.toArgb()
            textSize = with(density) { 10.sp.toPx() }
            typeface = lexendTypeface
            isAntiAlias = true
        }

        // 绘制频率
        textPaint.textAlign = android.graphics.Paint.Align.CENTER
        drawText(
            freqText,
            labelX + labelWidth / 2,
            labelY + padding + lineHeight * 0.7f,
            textPaint
        )

        // 绘制每条曲线的值
        curveData.forEachIndexed { index, curve ->
            val y = labelY + padding + lineHeight * (index + 1.7f)

            // 绘制曲线名称
            valuePaint.textAlign = android.graphics.Paint.Align.LEFT
            drawText(
                curve.name,
                labelX + padding,
                y,
                valuePaint
            )

            // 绘制SPL值
            valuePaint.textAlign = android.graphics.Paint.Align.RIGHT
            drawText(
                "${"%.1f".format(curve.spl)}dB",
                labelX + labelWidth - padding,
                y,
                valuePaint
            )
        }
    }
}

/**
 * 计算自适应的SPL范围
 */
private fun calculateAdaptiveSplRange(measurementData: MeasurementCondition?): Pair<Float, Float> {
    // 默认范围
    val defaultMinSpl = 60f
    val defaultMaxSpl = 120f

    if (measurementData == null || !measurementData.isValid()) {
        return Pair(defaultMinSpl, defaultMaxSpl)
    }

    val validSplValues = measurementData.getValidSplValues().filter { it.isFinite() && !it.isNaN() }
    if (validSplValues.isEmpty()) {
        return Pair(defaultMinSpl, defaultMaxSpl)
    }

    val dataMinSpl = validSplValues.minOrNull() ?: defaultMinSpl
    val dataMaxSpl = validSplValues.maxOrNull() ?: defaultMaxSpl

    // 检查默认范围是否能包含所有数据
    if (dataMinSpl >= defaultMinSpl && dataMaxSpl <= defaultMaxSpl) {
        return Pair(defaultMinSpl, defaultMaxSpl)
    }

    // 需要自适应调整 - 基于中位值计算
    val sortedValues = validSplValues.sorted()
    val median = if (sortedValues.size % 2 == 0) {
        (sortedValues[sortedValues.size / 2 - 1] + sortedValues[sortedValues.size / 2]) / 2f
    } else {
        sortedValues[sortedValues.size / 2]
    }

    // 确保最小范围差距为60dB，以中位值为中心
    val minRangeSpan = 60f
    val halfSpan = minRangeSpan / 2f

    // 以中位值为中心计算初始范围
    var calculatedMinSpl = median - halfSpan
    var calculatedMaxSpl = median + halfSpan

    // 调整范围以包含所有数据
    if (dataMinSpl < calculatedMinSpl) {
        val adjustment = calculatedMinSpl - dataMinSpl
        calculatedMinSpl -= adjustment
        calculatedMaxSpl -= adjustment
    }
    if (dataMaxSpl > calculatedMaxSpl) {
        val adjustment = dataMaxSpl - calculatedMaxSpl
        calculatedMaxSpl += adjustment
        calculatedMinSpl += adjustment
    }

    // 向下取整最小值到10的倍数，向上取整最大值到10的倍数
    val finalMinSpl = (calculatedMinSpl / 10f).toInt() * 10f
    val finalMaxSpl = ((calculatedMaxSpl / 10f).toInt() + 1) * 10f

    // 确保最终范围至少为60dB
    val finalSpan = finalMaxSpl - finalMinSpl
    if (finalSpan < minRangeSpan) {
        val center = (finalMinSpl + finalMaxSpl) / 2f
        val adjustedMinSpl = ((center - halfSpan) / 10f).toInt() * 10f
        val adjustedMaxSpl = adjustedMinSpl + minRangeSpan
        return Pair(adjustedMinSpl, adjustedMaxSpl)
    }

    return Pair(finalMinSpl, finalMaxSpl)
}

/**
 * 在指定频率处查找SPL值
 */
private fun findSplAtFrequency(frequencies: List<Float>, splValues: List<Float>, targetFrequency: Float): Float {
    if (frequencies.isEmpty() || splValues.isEmpty() || frequencies.size != splValues.size) return 60f

    // 过滤有效数据点
    val validIndices = frequencies.indices.filter { i ->
        frequencies[i] > 0f && splValues[i].isFinite() && !splValues[i].isNaN()
    }

    if (validIndices.isEmpty()) return 60f

    // 找到最接近的频率点
    val closestIndex = validIndices.minByOrNull { i -> abs(frequencies[i] - targetFrequency) } ?: validIndices[0]
    return splValues[closestIndex]
}

/**
 * 在两个测量数据之间插值
 */
private fun interpolateMeasurementData(
    from: cn.ykload.flowmix.data.MeasurementCondition?,
    to: cn.ykload.flowmix.data.MeasurementCondition?,
    progress: Float
): cn.ykload.flowmix.data.MeasurementCondition? {
    if (from == null) return to
    if (to == null) return from
    if (progress <= 0f) return from
    if (progress >= 1f) return to

    // 如果两个数据的频率点不匹配，直接切换
    val fromFreqs = from.getValidFrequencies()
    val toFreqs = to.getValidFrequencies()
    if (fromFreqs.size != toFreqs.size) {
        return if (progress < 0.5f) from else to
    }

    // 插值SPL值
    val fromSpls = from.getValidSplValues()
    val toSpls = to.getValidSplValues()
    val interpolatedSpls = fromSpls.mapIndexed { index, fromSpl ->
        val toSpl = toSpls.getOrElse(index) { fromSpl }
        fromSpl + (toSpl - fromSpl) * progress
    }

    // 创建插值后的测量数据
    return cn.ykload.flowmix.data.MeasurementCondition(
        title = "Interpolated: ${from.title} -> ${to.title}",
        frequencies = fromFreqs,
        spl_values = interpolatedSpls
    )
}

/**
 * 在两个目标数据之间插值
 */
private fun interpolateTargetData(
    from: cn.ykload.flowmix.data.MeasurementCondition?,
    to: cn.ykload.flowmix.data.MeasurementCondition?,
    progress: Float
): cn.ykload.flowmix.data.MeasurementCondition? {
    // 目标数据插值逻辑与测量数据相同
    return interpolateMeasurementData(from, to, progress)
}

/**
 * 绘制频响图表标签
 */
private fun DrawScope.drawFrequencyResponseChartLabel(
    labelText: String,
    labelScaleProgress: Float,
    labelPositionProgress: Float,
    labelAnimationProgress: Float,
    textColor: Color,
    backgroundColor: Color,
    density: androidx.compose.ui.unit.Density,
    context: android.content.Context
) {
    if (labelScaleProgress <= 0f) return

    val chartWidth = size.width
    val chartHeight = size.height
    val lexendTypeface = createLexendTypeface(context)

    // 主标题和副标题
    val mainTitle = labelText
    val subTitle = "点击修改 | 长按浏览"

    // 字体大小设置 - 主标题缩小一些
    val mainTitleLargeSize = 22.sp.toPx() // 从28sp减小到22sp
    val mainTitleSmallSize = 12.sp.toPx() // 从14sp减小到12sp
    val subTitleSize = 10.sp.toPx()

    // 计算当前主标题字体大小（通过缩放而不是改变字体大小）
    val currentMainTitleSize = mainTitleLargeSize // 保持字体大小不变
    val currentScale = 1f + (0.55f - 1f) * labelAnimationProgress // 从1.0缩放到0.55

    // 先测量文字尺寸用于计算目标位置
    val tempMainPaint = android.graphics.Paint().apply {
        textSize = mainTitleSmallSize
        typeface = lexendTypeface
    }
    val tempMainBounds = android.graphics.Rect()
    tempMainPaint.getTextBounds(mainTitle, 0, mainTitle.length, tempMainBounds)
    val finalMainTextWidth = tempMainBounds.width()

    // 计算标签位置
    val centerX = chartWidth / 2
    val centerY = chartHeight / 2
    // 目标位置：文字右边缘距离Card右边缘16dp
    val targetX = chartWidth - 16.dp.toPx() - finalMainTextWidth / 2
    val targetY = 20.dp.toPx() // 稍微下移一点

    // 插值计算当前位置
    val currentX = centerX + (targetX - centerX) * labelPositionProgress
    val currentY = centerY + (targetY - centerY) * labelPositionProgress

    // 计算透明度
    val mainAlpha = if (labelAnimationProgress < 1f) {
        1f
    } else {
        0.8f
    }

    // 副标题透明度 - 当主标题开始移动时淡出
    val subAlpha = if (labelPositionProgress > 0f) {
        (1f - labelPositionProgress).coerceAtLeast(0f)
    } else {
        1f
    }

    // 背景透明度 - 从1渐变到0
    val backgroundAlpha = if (labelAnimationProgress < 1f) {
        1f - labelAnimationProgress // 从1到0
    } else {
        0f // 动画完成后完全透明
    }

    // 绘制标签文字和背景
    drawContext.canvas.nativeCanvas.apply {
        val mainTextPaint = android.graphics.Paint().apply {
            color = textColor.copy(alpha = mainAlpha).toArgb()
            textSize = currentMainTitleSize
            typeface = lexendTypeface
            textAlign = android.graphics.Paint.Align.CENTER
            isAntiAlias = true
        }

        val subTextPaint = android.graphics.Paint().apply {
            color = textColor.copy(alpha = subAlpha).toArgb()
            textSize = subTitleSize
            typeface = lexendTypeface
            textAlign = android.graphics.Paint.Align.CENTER
            isAntiAlias = true
        }

        // 测量主标题和副标题尺寸
        val mainBounds = android.graphics.Rect()
        mainTextPaint.getTextBounds(mainTitle, 0, mainTitle.length, mainBounds)
        val mainTextWidth = mainBounds.width()
        val mainTextHeight = mainBounds.height()

        val subBounds = android.graphics.Rect()
        subTextPaint.getTextBounds(subTitle, 0, subTitle.length, subBounds)
        val subTextWidth = subBounds.width()
        val subTextHeight = subBounds.height()

        // 计算总体尺寸（包含两行文字）
        val totalWidth = maxOf(mainTextWidth, subTextWidth)
        val lineSpacing = 4.dp.toPx()
        val totalHeight = mainTextHeight + lineSpacing + subTextHeight

        // 计算实际绘制位置
        val actualX = currentX

        // 增大背景padding
        val padding = 12.dp.toPx() // 从8dp增加到12dp
        val backgroundLeft = actualX - totalWidth / 2 - padding
        val backgroundTop = currentY - totalHeight / 2 - padding
        val backgroundRight = actualX + totalWidth / 2 + padding
        val backgroundBottom = currentY + totalHeight / 2 + padding

        // 应用缩放
        save()
        scale(currentScale, currentScale, actualX, currentY)

        // 绘制圆角矩形背景
        val backgroundPaint = android.graphics.Paint().apply {
            color = backgroundColor.copy(alpha = backgroundAlpha).toArgb()
            isAntiAlias = true
        }
        val cornerRadius = 25.dp.toPx()
        drawRoundRect(
            backgroundLeft,
            backgroundTop,
            backgroundRight,
            backgroundBottom,
            cornerRadius,
            cornerRadius,
            backgroundPaint
        )

        // 绘制主标题 - 使用fontMetrics进行精确垂直居中
        val mainFontMetrics = mainTextPaint.fontMetrics
        val mainTextBaseline = currentY - totalHeight / 2 + mainTextHeight / 2 - (mainFontMetrics.ascent + mainFontMetrics.descent) / 2
        drawText(
            mainTitle,
            actualX,
            mainTextBaseline,
            mainTextPaint
        )

        // 绘制副标题（只在未开始移动时显示）
        if (subAlpha > 0f) {
            val subFontMetrics = subTextPaint.fontMetrics
            val subTextBaseline = currentY + totalHeight / 2 - subTextHeight / 2 - (subFontMetrics.ascent + subFontMetrics.descent) / 2
            drawText(
                subTitle,
                actualX,
                subTextBaseline,
                subTextPaint
            )
        }

        restore()
    }
}

/**
 * 绘制可见频段高亮
 */
private fun DrawScope.drawVisibleBandHighlight(
    visibleBandRange: IntRange,
    eqBands: List<cn.ykload.flowmix.data.EqBand>,
    chartLeft: Float,
    chartTop: Float,
    chartWidth: Float,
    chartHeight: Float,
    minFreq: Float,
    maxFreq: Float,
    curveColor: Color
) {
    if (eqBands.isEmpty() || visibleBandRange.isEmpty()) return

    // 确保索引在有效范围内
    val startIndex = visibleBandRange.first.coerceIn(0, eqBands.size - 1)
    val endIndex = visibleBandRange.last.coerceIn(0, eqBands.size - 1)

    if (startIndex >= eqBands.size || endIndex >= eqBands.size) return

    // 获取可见频段的频率范围
    val startFreq = eqBands[startIndex].frequency
    val endFreq = eqBands[endIndex].frequency

    // 计算频率在图表中的X坐标
    val startX = chartLeft + chartWidth * (kotlin.math.log10(startFreq) - kotlin.math.log10(minFreq)) / (kotlin.math.log10(maxFreq) - kotlin.math.log10(minFreq))
    val endX = chartLeft + chartWidth * (kotlin.math.log10(endFreq) - kotlin.math.log10(minFreq)) / (kotlin.math.log10(maxFreq) - kotlin.math.log10(minFreq))

    // 绘制高亮区域
    drawRect(
        color = curveColor.copy(alpha = 0.1f),
        topLeft = Offset(startX, chartTop),
        size = androidx.compose.ui.geometry.Size(endX - startX, chartHeight)
    )

    // 绘制边界线
    drawLine(
        color = curveColor.copy(alpha = 0.3f),
        start = Offset(startX, chartTop),
        end = Offset(startX, chartTop + chartHeight),
        strokeWidth = 2.dp.toPx()
    )
    drawLine(
        color = curveColor.copy(alpha = 0.3f),
        start = Offset(endX, chartTop),
        end = Offset(endX, chartTop + chartHeight),
        strokeWidth = 2.dp.toPx()
    )
}
