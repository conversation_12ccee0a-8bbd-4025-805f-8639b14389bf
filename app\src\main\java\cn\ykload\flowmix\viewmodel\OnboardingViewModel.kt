package cn.ykload.flowmix.viewmodel

import android.app.Application
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import cn.ykload.flowmix.permission.PermissionManager
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

/**
 * 引导页面ViewModel
 */
class OnboardingViewModel(application: Application) : AndroidViewModel(application) {
    
    private var permissionManager: PermissionManager? = null
    private var permissionLauncher: ActivityResultLauncher<Array<String>>? = null
    
    // UI状态
    private val _uiState = MutableStateFlow(OnboardingUiState())
    val uiState: StateFlow<OnboardingUiState> = _uiState.asStateFlow()
    
    init {
        checkDeviceSupport()
    }
    
    /**
     * 设置权限管理器
     */
    fun setPermissionManager(manager: PermissionManager) {
        permissionManager = manager
        checkPermissions()
    }
    
    /**
     * 设置权限启动器
     */
    fun setPermissionLauncher(launcher: ActivityResultLauncher<Array<String>>) {
        permissionLauncher = launcher
    }
    
    /**
     * 检查设备支持
     */
    private fun checkDeviceSupport() {
        viewModelScope.launch {
            // 这里可以添加设备支持检查逻辑
            _uiState.value = _uiState.value.copy(isDeviceSupported = true)
        }
    }
    
    /**
     * 检查权限状态
     */
    private fun checkPermissions() {
        permissionManager?.let { manager ->
            val audioPermission = manager.hasAudioPermission()
            val bluetoothPermission = manager.hasBluetoothPermission()
            val storagePermission = manager.hasStoragePermission()
            val notificationPermission = manager.hasNotificationPermission()

            Log.d("OnboardingViewModel", "检查权限状态: 音频=$audioPermission, 蓝牙=$bluetoothPermission, 存储=$storagePermission, 通知=$notificationPermission")

            _uiState.value = _uiState.value.copy(
                hasAudioPermission = audioPermission,
                hasBluetoothPermission = bluetoothPermission,
                hasStoragePermission = storagePermission,
                hasNotificationPermission = notificationPermission
            )
        }
    }
    
    /**
     * 下一步
     */
    fun nextStep() {
        val currentStep = _uiState.value.currentStep
        val nextStep = when (currentStep) {
            OnboardingStep.WELCOME -> OnboardingStep.PERMISSIONS
            OnboardingStep.PERMISSIONS -> OnboardingStep.READY
            OnboardingStep.READY -> OnboardingStep.COMPLETE
            OnboardingStep.COMPLETE -> OnboardingStep.COMPLETE
        }
        
        _uiState.value = _uiState.value.copy(currentStep = nextStep)
    }
    
    /**
     * 上一步
     */
    fun previousStep() {
        val currentStep = _uiState.value.currentStep
        val prevStep = when (currentStep) {
            OnboardingStep.WELCOME -> OnboardingStep.WELCOME
            OnboardingStep.PERMISSIONS -> OnboardingStep.WELCOME
            OnboardingStep.READY -> OnboardingStep.PERMISSIONS
            OnboardingStep.COMPLETE -> OnboardingStep.READY
        }
        
        _uiState.value = _uiState.value.copy(currentStep = prevStep)
    }
    
    /**
     * 请求权限
     */
    fun requestPermissions() {
        permissionManager?.let { manager ->
            val permissionsToRequest = manager.getPermissionsToRequest()
            
            if (permissionsToRequest.isNotEmpty()) {
                _uiState.value = _uiState.value.copy(isRequestingPermissions = true)
                permissionLauncher?.launch(permissionsToRequest)
            }
        }
    }
    
    /**
     * 权限请求结果处理
     */
    fun onPermissionResult(permissions: Map<String, Boolean>) {
        _uiState.value = _uiState.value.copy(isRequestingPermissions = false)
        checkPermissions()
        
        val deniedPermissions = permissions.filter { !it.value }.keys
        if (deniedPermissions.isNotEmpty()) {
            _uiState.value = _uiState.value.copy(
                errorMessage = "部分权限被拒绝，可能影响应用功能"
            )
        } else {
            _uiState.value = _uiState.value.copy(
                errorMessage = null
            )
        }
    }
    
    /**
     * 清除错误消息
     */
    fun clearError() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 跳过引导
     */
    fun skipOnboarding() {
        _uiState.value = _uiState.value.copy(currentStep = OnboardingStep.COMPLETE)
    }
    
    /**
     * 重置引导状态
     */
    fun resetOnboarding() {
        _uiState.value = OnboardingUiState()
        checkDeviceSupport()
        checkPermissions()
    }
}

/**
 * 引导步骤
 */
enum class OnboardingStep {
    WELCOME,      // 欢迎页面
    PERMISSIONS,  // 权限设置
    READY,        // 准备就绪
    COMPLETE      // 完成引导
}

/**
 * 引导页面UI状态
 */
data class OnboardingUiState(
    val currentStep: OnboardingStep = OnboardingStep.WELCOME,
    val isDeviceSupported: Boolean = true,
    val hasAudioPermission: Boolean = false,
    val hasBluetoothPermission: Boolean = false,
    val hasStoragePermission: Boolean = false,
    val hasNotificationPermission: Boolean = false,
    val isRequestingPermissions: Boolean = false,
    val errorMessage: String? = null
) {
    val hasAllPermissions: Boolean get() = hasAudioPermission && hasBluetoothPermission && hasStoragePermission && hasNotificationPermission
    val canProceed: Boolean get() = when (currentStep) {
        OnboardingStep.WELCOME -> isDeviceSupported
        OnboardingStep.PERMISSIONS -> hasAllPermissions
        OnboardingStep.READY -> true
        OnboardingStep.COMPLETE -> true
    }
}
